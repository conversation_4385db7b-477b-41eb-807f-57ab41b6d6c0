import 'package:app/core/app/routes.dart';
import 'package:app/core/globals.dart';
import 'package:app/features/auth/providers/auth_provider.dart';
import 'package:app/features/home/<USER>/tutorial_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Show tutorial after the screen is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showTutorial();
    });
  }

  void _showTutorial() {
    // For now, always show tutorial. Later we'll add logic to check if it's first time
    final rootContext = rootNavigatorKey.currentContext;
    if (rootContext != null) {
      TutorialOverlay.show(rootContext);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: Spacing.scaffoldInsets,
        child: Row(
          children: [
            Text(context.t.screens.home),
            const Spacer(),
            IconButton(
              onPressed: () {
                ref.read(authProvider.notifier).logout();
              },
              icon: const Icon(Icons.logout),
              tooltip: 'Logout',
            ),
            IconButton(
              onPressed: () {
                context.go(Routes.homeSettings);
              },
              icon: const Icon(Icons.settings),
            ),
          ],
        ),
      ),
    );
  }
}
