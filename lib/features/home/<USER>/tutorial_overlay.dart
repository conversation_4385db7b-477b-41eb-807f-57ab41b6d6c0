import 'package:app/core/globals.dart';
import 'package:app/features/general/general_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class TutorialOverlay extends StatefulWidget {
  const TutorialOverlay({super.key});

  static Future<void> show(BuildContext context) {
    return showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      useRootNavigator: true,
      pageBuilder: (context, animation, secondaryAnimation) {
        return const TutorialOverlay();
      },
    );
  }

  @override
  State<TutorialOverlay> createState() => _TutorialOverlayState();
}

class _TutorialOverlayState extends State<TutorialOverlay> with SingleTickerProviderStateMixin {
  bool _showBottomContainer = false;
  int _currentStep = 0; // 0-4 for 5 tabs
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _backgroundFadeAnimation;

  final List<MainPageTab> _tabs = [
    MainPageTab.home,
    MainPageTab.search,
    MainPageTab.matcher,
    MainPageTab.pockets,
    MainPageTab.business,
  ];

  final List<String> _stepTitles = [
    'Шаг 1 - Главная',
    'Шаг 2 - Поиск',
    'Шаг 3 - Матчер',
    'Шаг 4 - Карманы',
    'Шаг 5 - Бизнес',
  ];

  final List<String> _stepDescriptions = [
    'Здесь вы найдете основную информацию и новости.',
    'Используйте поиск для нахождения нужной информации.',
    'Матчер поможет найти подходящие варианты.',
    'Управляйте своими карманами и финансами.',
    'Раздел для бизнес-функций и возможностей.',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _backgroundFadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startTutorial() {
    setState(() {
      _showBottomContainer = true;
    });
    _animationController.forward();
  }

  void _nextStep() {
    if (_currentStep < _tabs.length - 1) {
      setState(() {
        _currentStep++;
      });
    } else {
      // Завершить tutorial
      Navigator.of(context).pop();
    }
  }

  void _skipTutorial() {
    Navigator.of(context).pop();
  }

  Widget _buildTrianglePointer() {
    // Calculate position based on current step
    final screenWidth = MediaQuery.of(context).size.width;
    final tabWidth = screenWidth / 5; // 5 tabs
    final trianglePosition = (_currentStep * tabWidth) + (tabWidth / 2) - 8; // Center triangle on tab

    return Container(
      width: 16,
      height: 8,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SizedBox.expand(
        child: Stack(
          children: [
            // Full screen welcome overlay with white semi-transparent background
            if (!_showBottomContainer)
              Positioned.fill(
                child: ColoredBox(
                  color: Colors.white.withValues(alpha: 0.95),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Добро пожаловать!',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Давайте покажем вам, как пользоваться приложением',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: Colors.black87,
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 48),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton(
                                  onPressed: _skipTutorial,
                                  style: OutlinedButton.styleFrom(
                                    side: const BorderSide(color: Colors.black54),
                                    foregroundColor: Colors.black54,
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                  ),
                                  child: const Text('Пропустить'),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: ElevatedButton(
                                  onPressed: _startTutorial,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.black,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 16),
                                  ),
                                  child: const Text('Начать'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // Tutorial steps overlay with dark background covering full screen
            if (_showBottomContainer)
              AnimatedBuilder(
                animation: _backgroundFadeAnimation,
                builder: (context, child) {
                  return Positioned.fill(
                    child: ColoredBox(
                      color: Colors.black.withValues(alpha: 0.85 * _backgroundFadeAnimation.value),
                      child: Stack(
                        children: [
                          // Tutorial step container positioned above bottom bar
                          Positioned(
                            bottom: 120, // Above the custom bottom bar
                            left: 16,
                            right: 16,
                            child: AnimatedBuilder(
                              animation: _fadeAnimation,
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _fadeAnimation.value,
                                  child: Container(
                                    padding: const EdgeInsets.all(24),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withValues(alpha: 0.2),
                                          blurRadius: 20,
                                          offset: const Offset(0, 10),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        // Drag indicator
                                        Container(
                                          width: 40,
                                          height: 4,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[300],
                                            borderRadius: BorderRadius.circular(2),
                                          ),
                                        ),
                                        const SizedBox(height: 20),
                                        Text(
                                          _stepTitles[_currentStep],
                                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          _stepDescriptions[_currentStep],
                                          style: Theme.of(context).textTheme.bodyLarge,
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 24),
                                        SizedBox(
                                          width: double.infinity,
                                          child: ElevatedButton(
                                            onPressed: _nextStep,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.black,
                                              foregroundColor: Colors.white,
                                              padding: const EdgeInsets.symmetric(vertical: 16),
                                            ),
                                            child: Text(_currentStep < _tabs.length - 1 ? 'Далее' : 'Завершить'),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),

                          // White triangle pointer above active tab
                          Positioned(
                            bottom: 100, // Just above the bottom bar
                            left: 0,
                            right: 0,
                            child: AnimatedBuilder(
                              animation: _fadeAnimation,
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _fadeAnimation.value,
                                  child: UnconstrainedBox(
                                    child: _buildTrianglePointer(),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

            // Custom Bottom Bar copy for tutorial (non-interactive)
            if (_showBottomContainer)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: _TutorialBottomBar(selectedTab: _tabs[_currentStep]),
              ),
          ],
        ),
      ),
    );
  }
}

/// Copy of CustomBottomNavBar for tutorial overlay (non-interactive)
class _TutorialBottomBar extends StatelessWidget {
  const _TutorialBottomBar({required this.selectedTab});

  final MainPageTab selectedTab;

  @override
  Widget build(BuildContext context) {
    final items = [
      (MainPageTab.home, SvgIcons.home),
      (MainPageTab.search, SvgIcons.search),
      (MainPageTab.matcher, SvgIcons.match),
      (MainPageTab.pockets, SvgIcons.pocket),
      (MainPageTab.business, SvgIcons.business),
    ];

    return Container(
      decoration: BoxDecoration(
        color: context.xColors.bottomBarBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(Spacing.radiusXl),
          topRight: Radius.circular(Spacing.radiusXl),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: Spacing.sm),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.map((item) {
              final isSelected = item.$1 == selectedTab;
              return Container(
                padding: Spacing.iconInsets,
                decoration: isSelected
                    ? BoxDecoration(
                        color: context.colors.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      )
                    : null,
                child: SvgPicture.asset(
                  item.$2,
                  width: Spacing.iconLg,
                  height: Spacing.iconLg,
                  colorFilter: ColorFilter.mode(
                    isSelected ? context.colors.primary : context.colors.onSurface,
                    BlendMode.srcIn,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for white triangle pointer
class _TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0) // Top center
      ..lineTo(0, size.height) // Bottom left
      ..lineTo(size.width, size.height) // Bottom right
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
