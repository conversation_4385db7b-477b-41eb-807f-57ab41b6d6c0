import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(statusBarColor: Colors.transparent),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        body: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                pinned: true,
                expandedHeight: 350,
                backgroundColor: Colors.transparent,
                systemOverlayStyle: SystemUiOverlayStyle.light.copyWith(
                  statusBarColor: Colors.transparent,
                ),
                stretch: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.asset(
                        Images.profile,
                        fit: BoxFit.cover,
                      ),
                      Container(
                        color: Colors.black.withValues(alpha: 0.3),
                      ),
                      Positioned(
                        bottom: 80,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            Text(
                              '@pocketchange',
                              style: context.typo.bodySmall?.copyWith(color: AppPalette.white),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              'Jillian Phyllis',
                              style: context.typo.displayLarge?.copyWith(color: AppPalette.white),
                            ),
                            const SizedBox(height: 10),
                          ],
                        ),
                      ),
                      Positioned(
                        bottom: 20,
                        left: 20,
                        right: 20,
                        child: GestureDetector(
                          onTap: () {},
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              vertical: 14,
                              horizontal: 24,
                            ),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  AppPalette.brandPrimary100,
                                  AppPalette.brandSecondary100,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Center(
                              child: Text(
                                context.t.profile.preview_profile,
                                style: context.typo.bodyLarge?.copyWith(
                                  color: context.colors.onPrimary,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Container(
                  decoration: BoxDecoration(
                    color: context.colors.surface,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(40)),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _buildSettingSection(context, [
                        context.t.profile.fields.name,
                        context.t.profile.fields.username,
                        context.t.profile.fields.password,
                        context.t.profile.fields.email_address,
                        context.t.profile.fields.address,
                      ]),
                      const SizedBox(height: 16),
                      _buildSettingSection(context, [
                        context.t.profile.fields.my_interests,
                        context.t.profile.fields.my_badges,
                      ]),
                      const SizedBox(height: 16),
                      _buildSettingSection(context, [
                        context.t.profile.visibility.profile_visibility,
                        context.t.profile.visibility.activity_visibility,
                      ]),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _buildSettingSection(BuildContext context, List<String> items) {
  return Container(
    decoration: BoxDecoration(
      color: context.colors.surface,
      borderRadius: BorderRadius.circular(16),
    ),
    child: Column(
      children: items
          .map(
            (title) => ListTile(
              title: Text(title),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {},
            ),
          )
          .toList(),
    ),
  );
}
